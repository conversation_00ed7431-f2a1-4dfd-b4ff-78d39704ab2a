#include "FirmwareWidget.h"
#include "Compatibility/CompatibilityChecker.h"
#include "Core/USBDeviceManager.h"
#include "Utils/Logger.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QHeaderView>
#include <QLabel>
#include <QSplitter>
#include <QPlainTextEdit>
#include <QScrollBar>

FirmwareWidget::FirmwareWidget(QWidget* parent) : QWidget(parent) {
    auto* mainLayout = new QVBoxLayout(this);

    // Create main splitter to separate firmware table and log display
    m_splitter = new QSplitter(Qt::Vertical, this);

    // Top section: Firmware table with refresh button
    QWidget* topWidget = new QWidget(this);
    auto* topLayout = new QVBoxLayout(topWidget);

    // Refresh button positioned above the USB drive list (firmware table)
    m_refreshButton = new QPushButton("Refresh USB Devices", this);
    m_refreshButton->setToolTip("Refresh the list of connected USB devices");
    topLayout->addWidget(m_refreshButton);

    // Firmware table
    m_tableView = new QTableView(this);
    m_model = new QStandardItemModel(this);
    m_tableView->setModel(m_model);
    m_tableView->horizontalHeader()->setStretchLastSection(true);
    m_tableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
    topLayout->addWidget(m_tableView);

    // Firmware action buttons
    auto* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(new QPushButton("Dump Firmware", this));
    buttonLayout->addWidget(new QPushButton("Flash Firmware", this));
    topLayout->addLayout(buttonLayout);

    // Bottom section: Log display panel
    QWidget* logWidget = new QWidget(this);
    auto* logLayout = new QVBoxLayout(logWidget);

    // Log panel header with clear button
    auto* logHeaderLayout = new QHBoxLayout();
    QLabel* logLabel = new QLabel("Application Log", this);
    logLabel->setFont(QFont("Arial", 10, QFont::Bold));
    m_clearLogButton = new QPushButton("Clear Log", this);
    m_clearLogButton->setMaximumWidth(100);
    logHeaderLayout->addWidget(logLabel);
    logHeaderLayout->addStretch();
    logHeaderLayout->addWidget(m_clearLogButton);
    logLayout->addLayout(logHeaderLayout);

    // Log display text widget
    m_logDisplay = new QPlainTextEdit(this);
    m_logDisplay->setReadOnly(true);
    m_logDisplay->setMaximumBlockCount(1000); // Limit to 1000 lines for performance
    m_logDisplay->setFont(QFont("Consolas", 9)); // Monospace font for logs
    m_logDisplay->setPlaceholderText("Application log messages will appear here...");
    logLayout->addWidget(m_logDisplay);

    // Add widgets to splitter
    m_splitter->addWidget(topWidget);
    m_splitter->addWidget(logWidget);

    // Set reasonable default sizes (70% for firmware table, 30% for log)
    m_splitter->setSizes({700, 300});
    m_splitter->setStretchFactor(0, 1); // Allow firmware section to stretch more
    m_splitter->setStretchFactor(1, 0); // Log section has fixed preference

    mainLayout->addWidget(m_splitter);
    setLayout(mainLayout);

    // Initialize the firmware table
    clear();

    // Connect signals
    connect(m_refreshButton, &QPushButton::clicked, this, &FirmwareWidget::onRefreshClicked);
    connect(m_clearLogButton, &QPushButton::clicked, this, &FirmwareWidget::clearLog);

    // Set up log capture from the Logger system
    setupLogCapture();
}

void FirmwareWidget::updateDevice(const USBDeviceInfo& device) {
    m_model->clear();
    m_model->setHorizontalHeaderLabels({"Version", "Date", "Verified", "Notes"});

    auto entries = CompatibilityChecker::findFirmwareForDevice(device);
    if (entries.isEmpty()) {
        QList<QStandardItem*> rowItems;
        QStandardItem* item = new QStandardItem("No compatible firmware found in database.");
        item->setData(Qt::AlignCenter, Qt::TextAlignmentRole);
        rowItems.append(item);
        m_model->appendRow(rowItems);
        m_tableView->setSpan(0, 0, 1, 4);
        return;
    }

    for (const auto& entry : entries) {
        for (const auto& file : entry.firmwareFiles) {
            QList<QStandardItem*> rowItems;
            rowItems.append(new QStandardItem(file.version));
            rowItems.append(new QStandardItem(file.releaseDate));
            QStandardItem* verifiedItem = new QStandardItem(file.verified ? "Yes" : "No");
            verifiedItem->setTextAlignment(Qt::AlignCenter);
            verifiedItem->setForeground(file.verified ? Qt::darkGreen : Qt::darkRed);
            rowItems.append(verifiedItem);
            rowItems.append(new QStandardItem(file.notes));
            m_model->appendRow(rowItems);
        }
    }
}

void FirmwareWidget::clear() {
    m_model->clear();
    m_model->setHorizontalHeaderLabels({"Version", "Date", "Verified", "Notes"});
    QList<QStandardItem*> rowItems;
    QStandardItem* item = new QStandardItem("Select a device to see compatible firmware.");
    item->setData(Qt::AlignCenter, Qt::TextAlignmentRole);
    rowItems.append(item);
    m_model->appendRow(rowItems);
    m_tableView->setSpan(0, 0, 1, 4);
}
