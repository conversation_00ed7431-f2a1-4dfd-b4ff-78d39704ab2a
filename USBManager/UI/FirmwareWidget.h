#ifndef FIRMWAREWIDGET_H
#define FIRMWAREWIDGET_H

#include <QWidget>
#include <QTableView>
#include <QStandardItemModel>
#include "Core/USBDeviceInfo.h"

class FirmwareWidget : public QWidget {
    Q_OBJECT
public:
    explicit FirmwareWidget(QWidget* parent = nullptr);
    void updateDevice(const USBDeviceInfo& device);
    void clear();

private:
    QTableView* m_tableView;
    QStandardItemModel* m_model;
};

#endif // FIRMWAREWIDGET_H
